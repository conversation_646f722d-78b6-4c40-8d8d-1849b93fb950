package com.xiyoufang.aij.mahjong;

import com.google.common.primitives.Bytes;
import com.google.common.primitives.Ints;
import com.xiyoufang.aij.core.ResponseFactory;
import com.xiyoufang.aij.mahjong.event.OperateEvent;
import com.xiyoufang.aij.mahjong.event.OutCardEvent;
import com.xiyoufang.aij.mahjong.record.*;
import com.xiyoufang.aij.mahjong.response.*;
import com.xiyoufang.aij.mahjong.struct.*;
import com.xiyoufang.aij.room.hero.Hero;
import com.xiyoufang.aij.room.table.EndMessage;
import com.xiyoufang.aij.room.table.Rule;
import com.xiyoufang.aij.room.table.Table;
import com.xiyoufang.aij.room.table.TableAbilityAdapter;
import com.xiyoufang.aij.utils.Json;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.EventQueue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Created by 席有芳 on 2018-12-20.
 *
 * <AUTHOR>
 */
public class MahjongTableAbility extends TableAbilityAdapter {
    /**
     * 日志记录器
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(MahjongTableAbility.class);

    /**
     * 无效牌
     */
    public static final byte INVALID_CARD = -1;
    /**
     * 最大人数
     */
    private final static int MAX_CHAIR = 4;
    /**
     * 库存麻将
     */
    private byte[] repertoryCard;
    /**
     * 玩家手牌转成Index
     */
    private byte[][] cardIndices;
    /**
     * 剩余数量
     */
    private int leftCardCount;
    /**
     * 庄家
     */
    private int banker = Table.INVALID_CHAIR;
    /**
     * 当前发的牌
     */
    private byte currCard;
    /**
     * 发牌数
     */
    private int dispatchCardNumber;
    /**
     * 当前椅子
     */
    private int currChair;
    /**
     * 出牌的椅子
     */
    private int outCardChair;
    /**
     * 出牌总数
     */
    private int[] discardCount;
    /**
     * 出牌的记录
     */
    private byte[][] discardCards;
    /**
     * 存放碰、杠、吃的牌
     */
    private Map<Integer, List<WeaveItem>> heroWeaveItems;
    /**
     * 存放胡牌结果
     */
    private Map<Integer, H> hHeroes;
    /**
     * 总局数
     */
    private int gameNumber = 8;
    /**
     * Action
     */
    private int[] action;
    /**
     * 选择的动作
     */
    private int[] performAction;
    /**
     * 响应
     */
    private boolean[] response;
    /**
     * 筛子点数
     */
    int diceSum = 0;
    /**
     * 还原椅子（用来处理玩家操作 点击 过，用来恢复）
     */
    private int resumeChair;
    /**
     * 抢杠状态
     */
    private boolean grabG;
    /**
     * 杠开标识
     */
    private boolean gOpen;
    /**
     * 逻辑
     */
    private MahjongLogic mahjongLogic = new MahjongLogic();
    /**
     * 游戏状态
     */
    private MahjongGameStatus gameStatus = MahjongGameStatus.GAME_PREPARE;
    /**
     * 动作对应的牌
     */
    private byte[] actionCard;
    /**
     * 动作对应的牌
     */
    private byte[][] actionCards;
    /**
     * 杠分
     */
    private static final int G_SCORE = 0;
    /**
     * 胡分
     */
    private static final int H_SCORE = 1;
    /**
     * 麻将游戏记录
     */
    private MahjongRecord mahjongRecord = new MahjongRecord();
    /**
     * 当前局游戏记录
     */
    private MahjongGameRecord mahjongGameRecord;
    /**
     * 出牌倒计时时间（毫秒）
     */
    private static final int PLAY_CARD_TIMEOUT = 30000;

    /**
     * 当前倒计时剩余时间
     */
    private int countdown = PLAY_CARD_TIMEOUT / 1000;
    /**
     * 出牌定时执行器
     */
    private ScheduledExecutorService scheduler;
    /**
     * 定时任务Future
     */
    private ScheduledFuture<?> countdownFuture;
    /**
     * 当前倒计时玩家
     */
    private int countdownChair = Table.INVALID_CHAIR;

    /**
     * 当前正在检查仰操作的玩家
     */
    private int yangCheckingChair = Table.INVALID_CHAIR;

    /**
     * 已经检查过仰操作的玩家数量
     */
    private int yangCheckedCount = 0;

    /**
     * 记录玩家牌信息的方法
     * 
     * @param chair 玩家座位号
     * @param action 动作类型
     * @param card 操作的牌
     */
    private void logPlayerCardInfo(int chair, String action, byte card) {
        Hero hero = table.getHero(chair);
        String playerName = hero != null ? hero.getNickName() : "未知玩家";
        StringBuilder cardInfo = new StringBuilder();
        
        // 记录手牌信息
        byte[] handCards = MahjongKit.switchIndicesToCards(cardIndices[chair]);
        cardInfo.append("手牌: [");
        for (int i = 0; i < handCards.length; i++) {
            if (i > 0) cardInfo.append(", ");
            cardInfo.append(MahjongKit.getCardTitle(handCards[i]));
            cardInfo.append("(").append(String.format("0x%02X", handCards[i])).append(")");
        }
        cardInfo.append("]");
        
        // 记录碰和杠的牌信息
        List<WeaveItem> weaveItems = getWeaveItems(chair);
        if (weaveItems != null && !weaveItems.isEmpty()) {
            cardInfo.append(", 操作牌: [");
            for (int i = 0; i < weaveItems.size(); i++) {
                WeaveItem item = weaveItems.get(i);
                if (i > 0) cardInfo.append(", ");
                cardInfo.append(item.getWeaveType().getName()).append(": ");
                cardInfo.append(MahjongKit.getCardTitle(item.getCenterCard()));
                cardInfo.append("(").append(String.format("0x%02X", item.getCenterCard())).append(")");
            }
            cardInfo.append("]");
        }
        
        // 记录当前操作的牌
        String cardTitle = MahjongKit.getCardTitle(card);
        String cardHex = String.format("0x%02X", card);
        
        LOGGER.info("玩家 {} (座位 {}) {} 牌: {} ({}), {}", 
                playerName, chair, action, cardTitle, cardHex, cardInfo.toString());
    }

    /**
     * 发送当前游戏场景
     *
     * @param chair chair
     */
    @Override
    public void currentGameScene(int chair) { //发送当前场景
        GameStatusResponse gameStatusResponse = ResponseFactory.success(GameStatusResponse.class, "游戏状态");
        gameStatusResponse.setStatus(gameStatus.getValue());
        sendChairs(gameStatusResponse, chair);
        switch (gameStatus) {
            case GAME_PREPARE:
                sendChairs(ResponseFactory.success(PrepareGameSceneResponse.class, "游戏预备"), chair);
                break;
            case GAME_PLAYING:
                PlayingGameSceneResponse playingGameSceneResponse = ResponseFactory.success(PlayingGameSceneResponse.class, "游戏场景");
                playingGameSceneResponse.setChairCount(chairCount);
                playingGameSceneResponse.setChair(chair);
                playingGameSceneResponse.setBanker(banker);
                playingGameSceneResponse.setCurrent(currChair);
                playingGameSceneResponse.setDiceSum(diceSum);
                playingGameSceneResponse.setLeftCardCount(leftCardCount);
                playingGameSceneResponse.setTotalNumber(gameNumber);
                playingGameSceneResponse.setCurrentNumber(currGameNumber);
                playingGameSceneResponse.setCurrCard(currCard);
                playingGameSceneResponse.setAction(action[chair]);
                playingGameSceneResponse.setActionCard(actionCard[chair]);
                playingGameSceneResponse.setActionCards(actionCards[chair]);
                playingGameSceneResponse.setCards(MahjongKit.switchIndicesToCards(cardIndices[chair]));
                playingGameSceneResponse.setDiscardCount(discardCount);
                playingGameSceneResponse.setDiscardCards(discardCards);
                playingGameSceneResponse.setWeaveItems(getWeaveItemResponses());
                playingGameSceneResponse.setScores(readScores());
                sendChairs(playingGameSceneResponse, chair);
                break;
        }
    }

    /**
     * 获取组合
     *
     * @return WeaveItemResponse
     */
    private WeaveItemResponse[][] getWeaveItemResponses() {
        WeaveItemResponse[][] weaveItems = new WeaveItemResponse[chairCount][];
        for (int i = 0; i < chairCount; i++) {
            List<WeaveItem> items = getWeaveItems(i);
            weaveItems[i] = new WeaveItemResponse[items.size()];
            for (int ii = 0; ii < items.size(); ii++) {
                WeaveItem weaveItem = items.get(ii);
                WeaveItemResponse weaveItemResponse = new WeaveItemResponse();
                weaveItemResponse.setCenterCard(weaveItem.getCenterCard());
                weaveItemResponse.setOpen(weaveItem.isOpen());
                weaveItemResponse.setProvider(weaveItem.getProvider());
                weaveItemResponse.setWeaveType(weaveItem.getWeaveType().getValue());
                weaveItems[i][ii] = weaveItemResponse;
            }
        }
        return weaveItems;
    }

    /**
     * 桌子开始
     *
     * @param rule 规则
     */
    @Override
    public void onInit(Rule rule) {
        LOGGER.info("创建桌子成功,规则为{}", rule.toJson()); //在onCreate方法里初始化全局变量，例如规则，局数
        gameNumber = 4;
    }

    /**
     * 桌子开始事件
     */
    @Override
    protected void onStart() {
        mahjongRecord.setTableNo(tableNo);                  //桌子编号
        for (int i = 0; i < chairCount; i++) {              //设置游戏记录的玩家信息，初始化统计信息
            MahjongPlayerRecord mahjongPlayerRecord = new MahjongPlayerRecord();
            Hero hero = this.table.getHero(i);
            mahjongPlayerRecord.setAvatar(hero.getAvatar());
            mahjongPlayerRecord.setUserId(hero.getUserId());
            mahjongPlayerRecord.setUserName(hero.getUserName());
            mahjongPlayerRecord.setNickName(hero.getNickName());
            mahjongPlayerRecord.setGender(hero.getGender());
            mahjongPlayerRecord.setAddress(hero.getAddress());
            mahjongPlayerRecord.setLatitude(hero.getLatitude());
            mahjongPlayerRecord.setLongitude(hero.getLongitude());
            mahjongPlayerRecord.setIp(hero.getIp());
            mahjongRecord.getMahjongPlayerRecords().add(mahjongPlayerRecord);
        }
    }

    /**
     * 开始重置桌子
     */
    @Override
    public void onGameReset() {
        repertoryCard = MahjongKit.shuffle(MahjongConst.CARDS, 2);
        leftCardCount = repertoryCard.length;

        // 临时测试：直接修改牌堆，确保第一个玩家能拿到中发白三张牌
        // 发牌顺序：每人发13张牌，从leftCardCount位置开始向前取牌
        // 第一个玩家的牌在位置：leftCardCount-13 到 leftCardCount-1
        int firstPlayerStart = leftCardCount - 13; // 第一个玩家的牌开始位置

        // 将中发白三张牌放到第一个玩家的牌位中
        repertoryCard[firstPlayerStart] = (byte) 0x35; // 中
        repertoryCard[firstPlayerStart + 1] = (byte) 0x36; // 发
        repertoryCard[firstPlayerStart + 2] = (byte) 0x37; // 白
        cardIndices = new byte[chairCount][];
        currCard = INVALID_CARD;
        currChair = Table.INVALID_CHAIR;
        outCardChair = Table.INVALID_CHAIR;
        resumeChair = Table.INVALID_CHAIR;
        dispatchCardNumber = 0;
        diceSum = 0;
        discardCount = new int[chairCount];
        discardCards = new byte[chairCount][MahjongConst.CARDS.length];
        heroWeaveItems = new ConcurrentHashMap<Integer, List<WeaveItem>>();
        hHeroes = new ConcurrentHashMap<>();
        action = new int[chairCount];
        performAction = new int[chairCount];
        response = new boolean[chairCount];
        actionCard = new byte[chairCount];
        actionCards = new byte[chairCount][];
        Arrays.fill(actionCards, new byte[]{});
        grabG = false;
        gOpen = false;

        //游戏记录
        mahjongGameRecord = new MahjongGameRecord();
        mahjongGameRecord.setRepertory(repertoryCard);
        mahjongRecord.getMahjongGameRecords().add(mahjongGameRecord);
    }

    /**
     * 游戏开始
     */
    @Override
    public void onGameStart() {
        LOGGER.info("开始游戏，当前为第{}局，总计{}局", currGameNumber, gameNumber);
        gameStatus = MahjongGameStatus.GAME_PLAYING;
        diceSum = MahjongKit.diceSum(2);
        if (banker == Table.INVALID_CHAIR) {    //丢筛子确定庄家
            int i = diceSum % chairCount;
            banker = i == 0 ? (chairCount - 1) : i - 1;
        }

        // 为测试仰操作，给每个玩家发中发白牌
        for (int i = 0; i < chairCount; i++) {
            leftCardCount -= (MahjongConst.MAX_COUNT - 1);
            byte[] cards = new byte[MahjongConst.MAX_COUNT - 1];
            System.arraycopy(repertoryCard, leftCardCount, cards, 0, MahjongConst.MAX_COUNT - 1);

            // 测试：确保每个玩家都有中发白牌
            if (cards.length >= 3) {
                cards[0] = (byte) 0x35; // 中
                cards[1] = (byte) 0x36; // 发
                cards[2] = (byte) 0x37; // 白
                System.out.println("为玩家" + i + "分配中发白牌用于测试仰操作");
            }

            cardIndices[i] = MahjongKit.switchCardsToIndices(cards); //每人发 13 张牌，将card转成index模式
        }
        for (int i = 0; i < chairCount; i++) {//封装命令，将牌信息发送给用户
            GameStartEventResponse response = ResponseFactory.success(GameStartEventResponse.class, "游戏开始");
            response.setChairCount(chairCount);
            response.setChair(i);
            response.setDiceSum(diceSum);
            response.setBanker(banker);
            response.setCurrent(banker);
            response.setLeftCardCount(leftCardCount);
            response.setCards(MahjongKit.switchIndicesToCards(cardIndices[i]));
            response.setTotalNumber(gameNumber);
            response.setCurrentNumber(currGameNumber);
            response.setScores(readScores());
            sendChairs(response, i);
            MahjongGameStartRecord mahjongGameStartRecord = new MahjongGameStartRecord();
            mahjongGameStartRecord.setCards(response.getCards());
            mahjongGameRecord.getMahjongGameStartRecord().add(mahjongGameStartRecord);
        }
        mahjongGameRecord.setBanker(banker);
        printLog();
        dispatchCard(banker, false); //给庄家发牌
    }

    /**
     * 获取 weaveItems
     *
     * @param chair chair
     * @return WeaveItems
     */
    private List<WeaveItem> getWeaveItems(int chair) {
        if (!heroWeaveItems.containsKey(chair)) {
            heroWeaveItems.put(chair, new ArrayList<WeaveItem>());
        }
        return heroWeaveItems.get(chair);
    }

    /**
     * 发牌
     *
     * @param chair 椅子
     * @param tail  是否杠拿牌
     */
    private void dispatchCard(int chair, boolean tail) {
        if (leftCardCount > 0) {   //结束游戏
            gOpen = tail;
            currChair = chair;
            dispatchCardNumber++;
            currCard = repertoryCard[--leftCardCount];       //发的牌
            resetCycleActionVar();
            byte index = MahjongKit.switchCardToIndex(currCard);
            cardIndices[currChair][index] += 1;
            DispatchCardEventResponse response = ResponseFactory.success(DispatchCardEventResponse.class, "发牌");
            response.setCard(currCard);
            response.setChair(chair);
            response.setTail(tail);
            sendChairs(response, chair);    //自己显示、其他隐藏
            response.setCard(INVALID_CARD);
            sendExceptChairs(response, chair);
            addMahjongGameActionRecord(MahjongAction.DISPATCH, chair, chair, currCard, new byte[]{}, 0);
            //校验杠和校验胡牌
            G g = mahjongLogic.estimateG(cardIndices[chair], currCard, getWeaveItems(chair), true, chair);
            H h = mahjongLogic.estimateH(cardIndices[chair], currCard, getWeaveItems(chair), true, chair, Source.IN, dispatchCardNumber);

            // 检查仰操作（只在河牌数量为0时允许）
            int totalDiscardCount = getTotalDiscardCount();
            System.out.println("=== 仰操作前置检查 ===");
            System.out.println("当前玩家椅子: " + chair);
            System.out.println("总河牌数量: " + totalDiscardCount);

            boolean hasAction = false;
            if (totalDiscardCount == 0) {
                System.out.println("河牌数量为0，开始按顺序检查仰操作");
                // 从庄家开始按顺序检查仰操作
                hasAction = startYangOperationCheck();

                // 如果没有仰操作，则检查当前玩家的其他操作
                if (!hasAction) {
                    hasAction = sendOperateNotify(chair, null, g, h, null, null, currCard, chair);
                }
            } else {
                System.out.println("河牌数量不为0，跳过仰操作检查");
                hasAction = sendOperateNotify(chair, null, g, h, null, null, currCard, chair);
            }

            // 在发牌后记录玩家手牌信息
            if (currCard != INVALID_CARD) {
                logPlayerCardInfo(chair, "摸", currCard);
            }

            // 只有在没有仰操作时才为当前玩家启动倒计时
            if (!hasAction || totalDiscardCount > 0) {
                // 为当前玩家启动倒计时
                startPlayCardCountdown(chair);
            } else {
                System.out.println("有仰操作，等待仰操作完成");
                // 设置currChair为INVALID_CHAIR，表示当前没有玩家在出牌阶段
                currChair = Table.INVALID_CHAIR;
            }
        } else {
            gameEnd();
        }
    }

    /**
     * 重置操作相关变量
     */
    private void resetCycleActionVar() {
        Arrays.fill(action, 0);
        Arrays.fill(performAction, 0);
        Arrays.fill(response, false);
        Arrays.fill(actionCard, (byte) 0);
        Arrays.fill(actionCards, new byte[]{});
    }

    /**
     * @param chair    当前
     * @param p        碰
     * @param g        杠
     * @param h        胡
     * @param c        吃
     * @param y        仰
     * @param card     牌
     * @param provider 供应者
     */
    private boolean sendOperateNotify(int chair, P p, G g, H h, C c, Y y, byte card, int provider) {
        if (p != null) action[chair] |= MahjongConst.P;   //有碰
        if (g != null) action[chair] |= MahjongConst.G;   //有杠
        if (h != null) action[chair] |= MahjongConst.H;   //有胡
        if (c != null) action[chair] |= MahjongConst.C;   //有吃
        if (y != null) action[chair] |= MahjongConst.Y;   //有仰
        
        if (action[chair] != 0x00) {    //发送操作通知
            if (action[chair] != MahjongConst.N) {
                OperateNotifyEventResponse response = ResponseFactory.success(OperateNotifyEventResponse.class, "操作通知");
                response.setAction(action[chair]);
                response.setChair(chair);
                response.setProvider(provider);
                actionCard[chair] = card;
                
                if (p != null) response.setCard(card);
                if (h != null) response.setCard(card);
                
                if (c != null && c.getCombinations().size() > 0) {
                    // 处理吃牌时发送的组合信息（可能有多种吃法）
                    byte[][] combinations = new byte[c.getCombinations().size()][];
                    for (int i = 0; i < c.getCombinations().size(); i++) {
                        combinations[i] = c.getCombinations().get(i);
                    }
                    response.setCombinations(combinations);
                    response.setCard(card);
                }
                
                if (g != null) {
                    byte[] cards = new byte[g.gs.size()];
                    boolean isNow = false;
                    for (int i = 0; i < g.gs.size(); i++) {
                        cards[i] = g.gs.get(0).card;
                        if (cards[i] == card) {
                            isNow = true;
                        }
                    }
                    response.setCards(cards);
                    response.setCard(isNow ? card : cards[0]); //杠,如果杠的牌不是当前的牌,者默认Card为第一张
                    actionCards[chair] = cards;
                }

                if (y != null) {
                    response.setCard((byte)0); // 仰操作不需要特定的card
                    response.setCombination(y.getCards()); // 设置中发白三张牌
                }
                
                sendChairs(response, chair);
                addMahjongGameActionRecord(MahjongAction.NOTIFY, response.getChair(), response.getProvider(), response.getCard(), response.getCards(), response.getAction());
            }
            return true;
        }
        return false;
    }

    // 增加一个兼容旧方法的重载，以保持向后兼容性
    private boolean sendOperateNotify(int chair, P p, G g, H h, byte card, int provider) {
        return sendOperateNotify(chair, p, g, h, null, null, card, provider);
    }

    // 增加一个兼容旧方法的重载，以保持向后兼容性
    private boolean sendOperateNotify(int chair, P p, G g, H h, C c, byte card, int provider) {
        return sendOperateNotify(chair, p, g, h, c, null, card, provider);
    }

    /**
     * 添加动作记录
     *
     * @param mahjongAction mahjongAction
     * @param chair         chair
     * @param provider      provider
     * @param card          card
     * @param cards         cards
     * @param action        action
     */
    private void addMahjongGameActionRecord(MahjongAction mahjongAction, int chair, int provider, byte card, byte[] cards, int action) {
        MahjongGameActionRecord mahjongGameActionRecord = new MahjongGameActionRecord();
        mahjongGameActionRecord.setMahjongAction(mahjongAction);    //动作类型
        mahjongGameActionRecord.setChair(chair);                    //椅子
        mahjongGameActionRecord.setProvider(provider);              //供应者
        mahjongGameActionRecord.setCard(card);                      //牌
        mahjongGameActionRecord.setCards(cards);                    //牌
        mahjongGameActionRecord.setAction(action);                  //操作
        mahjongGameRecord.getMahjongGameActionRecords().add(mahjongGameActionRecord);
        printLog();
    }

    /**
     * 玩家出牌事件
     *
     * @param chair 椅子
     * @param hero  玩家
     * @param event 事件
     */
    public synchronized void outCard(int chair, Hero hero, OutCardEvent event) {
        // 出牌前先进行校验
        if (currChair != chair) {  //玩家校验
            sendChairs(ResponseFactory.error(ErrorEventResponse.class, "不允许出牌"), chair);
            return;
        }
        
        // 出牌逻辑...
        byte outCard = event.getCard();
        if (cardIndices[chair][MahjongKit.switchCardToIndex(outCard)] == 0) {
            sendChairs(ResponseFactory.error(ErrorEventResponse.class, "没有对应的牌"), chair);
            return;
        }
        if (MahjongKit.isValidCard(outCard)) {
            sendChairs(ResponseFactory.error(ErrorEventResponse.class, "异常出牌"), chair);
            return;
        }
        
        // 记录玩家出牌信息
        logPlayerCardInfo(chair, "出", outCard);



        // 出牌验证通过，现在可以安全地取消倒计时
        if (chair == countdownChair) {
            cancelPlayCardTimer(chair);
        }
        
        discardCards[chair][discardCount[chair]] = outCard;
        currCard = outCard;
        discardCount[chair] += 1;
        outCardChair = chair;
        resetCycleActionVar();
        MahjongKit.removeIndicesByCards(cardIndices[chair], outCard);
        OutCardEventResponse response = ResponseFactory.success(OutCardEventResponse.class, "出牌");
        response.setCard(outCard);
        response.setChair(chair);
        sendTable(response);
        addMahjongGameActionRecord(MahjongAction.OUT, chair, chair, outCard, new byte[]{}, 0);
        
        // 检查其他玩家是否有动作（碰、杠、胡）
        boolean hasAction = false;
        for (int i = 0; i < chairCount; i++) {
            action[i] = MahjongConst.N;
            if (i != chair) {    //排除自己
                P p = mahjongLogic.estimateP(cardIndices[i], outCard, i);
                G g = mahjongLogic.estimateG(cardIndices[i], outCard, getWeaveItems(i), false, i);
                H h = mahjongLogic.estimateH(cardIndices[i], outCard, getWeaveItems(i), false, chair, Source.OUT, dispatchCardNumber);
                
                // 检查是否可以吃牌，只有下家才能吃
                C c = null;
                if (nextChair(chair) == i) {
                    c = mahjongLogic.estimateC(cardIndices[i], outCard, chair);
                }
                
                hasAction |= sendOperateNotify(i, p, g, h, c, outCard, chair);
            }
        }
        
        // 为下一个玩家启动倒计时，即使其他玩家有可能的操作
        int nextChair = nextChair(chair);
        resumeChair = nextChair;
        
        if (!hasAction) {
            // 没有玩家有操作，直接发牌给下一位玩家
            LOGGER.info("没有玩家有操作，轮到下一个玩家{}出牌", nextChair);
            dispatchCard(nextChair, false);
        } else {
            // 其他玩家有操作，但我们仍然为下一个玩家启动倒计时
            // 如果有玩家在倒计时结束前操作，会处理该操作
            // 如果没有玩家操作，倒计时结束后自动轮到下一个玩家
            currChair = Table.INVALID_CHAIR; // 标记当前没有玩家可以出牌
            
            // 为下一个玩家启动倒计时
            LOGGER.info("有玩家可以操作，但仍然为下一个玩家{}启动倒计时", nextChair);
            startPlayCardCountdown(nextChair);
        }
    }
    
    /**
     * 获取下一位玩家
     * 
     * @param chair 当前玩家
     * @return 下一位玩家位置
     */
    public int nextChair(int chair) {
        return (chair + 1) % chairCount;
    }

    /**
     * 操作，处理操作通知
     *
     * @param chair chair
     * @param hero  hero
     * @param event event
     */
    public void operate(int chair, Hero hero, OperateEvent event) {
        int chairAction = event.getAction();
        byte card = event.getCard();
        if (response[chair]) {
            sendChairs(ResponseFactory.error(ErrorEventResponse.class, "重复操作"), chair);
            return;
        }
        
        // 验证牌的有效性（仰操作特殊处理）
        if (chairAction != MahjongConst.Y) {
            if (MahjongKit.isValidCard(card)
                    || (actionCard[chair] != card
                    && (actionCards[chair] != null && !Bytes.asList(actionCards[chair]).contains(card)))) {
                sendChairs(ResponseFactory.error(ErrorEventResponse.class, "无效的牌"), chair);
                return;
            }
        } else {
            // 仰操作不需要验证具体的牌，只需要验证动作权限
            if ((action[chair] & MahjongConst.Y) == 0) {
                sendChairs(ResponseFactory.error(ErrorEventResponse.class, "没有仰操作权限"), chair);
                return;
            }
        }

        // 操作验证通过，现在可以安全地取消倒计时
        if (chair == countdownChair) {
            cancelPlayCardTimer(chair);
        }

        // 如果是仰操作，重置仰操作检查状态
        if (chairAction == MahjongConst.Y && yangCheckingChair == chair) {
            yangCheckingChair = Table.INVALID_CHAIR;
        }
        
        // 标记该玩家已响应
        response[chair] = true;
        byte[] chairCardIndices = cardIndices[chair];
        List<WeaveItem> weaveItems = getWeaveItems(chair);
        OperateResultEventResponse eventResponse = ResponseFactory.success(OperateResultEventResponse.class);
        
        // 处理玩家操作
        if (currChair == Table.INVALID_CHAIR) {
            int ac = action[chair];
            int targetAction = chairAction;
            List<Integer> targetChairs = new ArrayList<Integer>();
            targetChairs.add(chair);    //先添加自己
            int targetRank = MahjongKit.getActionRank(targetAction);
            performAction[chair] = targetAction;
            boolean wait = false;
            
            // 检查是否需要等待其他玩家的决定
            for (int i = 0; i < chairCount; i++) {
                if (i != chair) {
                    int userAction = (!response[i]) ? action[i] : performAction[i];
                    if (userAction == 0x00) {                                                   //过滤无动作
                        continue;
                    }
                    int userRank = MahjongKit.getActionRank(userAction);                         //获取自身动作优先级
                    if (targetRank < userRank) {
                        wait = true;
                        targetAction = userAction;
                    } else if (targetRank == userRank) {
                        wait = true;                                                             //一炮多响逻辑
                        targetChairs.add(i);
                    }
                }
            }
            
            // 如果需要等待其他玩家，不进行任何操作
            if (wait) {
                return;
            }
            
            // 取消当前的倒计时，因为已经有玩家操作了
            if (scheduler != null) {
                countdownFuture.cancel(true);
                scheduler.shutdownNow();
                scheduler = null;
                countdownChair = Table.INVALID_CHAIR;
            }
            
            currChair = chair;
            currCard = INVALID_CARD;
            resetCycleActionVar();
            switch (targetAction) {
                case MahjongConst.N:  //过
                    eventResponse.setMessage("过");
                    eventResponse.setAction(MahjongConst.N);
                    eventResponse.setCard(card);
                    eventResponse.setChair(chair);
                    eventResponse.setProvider(outCardChair);
                    sendChairs(eventResponse, chair);
                    addMahjongGameActionRecord(MahjongAction.N, chair, outCardChair, card, new byte[]{}, MahjongConst.N);

                    // 检查是否是仰操作的"过"
                    if (yangCheckingChair == chair) {
                        System.out.println("玩家" + chair + "选择过，不进行仰操作，检查下一个玩家");
                        // 继续检查下一个玩家的仰操作
                        boolean hasNextYangAction = checkNextPlayerYangOperation();
                        if (!hasNextYangAction) {
                            // 没有更多玩家可以仰牌，庄家开始正常出牌
                            System.out.println("所有玩家仰操作检查完毕，庄家开始正常出牌");
                            startPlayCardCountdown(banker);
                        }
                    } else {
                        // 普通的过操作，继续正常流程
                        dispatchCard(resumeChair, false);
                    }
                    break;
                case MahjongConst.P:  //碰
                    P p = mahjongLogic.estimateP(cardIndices[chair], card, outCardChair);
                    if (p != null) {
                        MahjongKit.removeIndicesByCards(cardIndices[chair], p.getCard(), p.getCard());//移除手上的牌
                        weaveItems.add(new WeaveItem(WeaveType.P, p.getCard(), true, outCardChair, true));
                        
                        // 记录玩家碰牌信息
                        logPlayerCardInfo(chair, "碰", card);
                        
                        eventResponse.setMessage("碰");
                        eventResponse.setAction(MahjongConst.P);
                        eventResponse.setCard(card);
                        eventResponse.setChair(chair);
                        eventResponse.setProvider(outCardChair);
                        discardCount[outCardChair] = discardCount[outCardChair] - 1;
                        discardCards[outCardChair][discardCount[outCardChair]] = 0;
                        sendTable(eventResponse);
                        addMahjongGameActionRecord(MahjongAction.P, chair, outCardChair, card, new byte[]{}, MahjongConst.P);
                        // 碰后需要启动倒计时，玩家需要出一张牌
                        startPlayCardCountdown(chair);
                    }
                    break;
                case MahjongConst.C:  //吃
                    C c = mahjongLogic.estimateC(cardIndices[chair], card, outCardChair);
                    if (c != null && c.getCombinations().size() > 0) {
                        // 使用第一个可用的吃牌组合（简化处理，不需要玩家选择）
                        byte[] selectedCombination = c.getCombinations().get(0);

                        // 从手牌中移除组合中的其他两张牌（不包括出牌者打出的牌）
                        for (byte comboCard : selectedCombination) {
                            if (comboCard != card) { // 不是出牌者打出的牌
                                MahjongKit.removeIndicesByCards(cardIndices[chair], comboCard);
                            }
                        }

                        // 添加到组合牌中，使用组合中的中间牌作为centerCard
                        byte centerCard = selectedCombination[1]; // 顺子的中间牌
                        weaveItems.add(new WeaveItem(WeaveType.C, centerCard, true, outCardChair, true));

                        // 记录玩家吃牌信息
                        logPlayerCardInfo(chair, "吃", card);

                        eventResponse.setMessage("吃");
                        eventResponse.setAction(MahjongConst.C);
                        eventResponse.setCard(card);
                        eventResponse.setChair(chair);
                        eventResponse.setProvider(outCardChair);
                        eventResponse.setCombination(selectedCombination);

                        // 从上家的出牌记录中移除吃的牌
                        discardCount[outCardChair] = discardCount[outCardChair] - 1;
                        discardCards[outCardChair][discardCount[outCardChair]] = 0;

                        sendTable(eventResponse);
                        addMahjongGameActionRecord(MahjongAction.C, chair, outCardChair, card, selectedCombination, MahjongConst.C);

                        // 吃后需要启动倒计时，玩家需要出一张牌
                        startPlayCardCountdown(chair);
                    }
                    break;
                case MahjongConst.Y:  //仰
                    Y y = mahjongLogic.estimateY(cardIndices[chair], chair);
                    if (y != null && getTotalDiscardCount() == 0) {
                        LOGGER.info("玩家{}仰操作验证通过，移除中发白三张牌", chair);

                        // 从手牌中移除中发白三张牌
                        MahjongKit.removeIndicesByCards(cardIndices[chair], y.getCards());

                        // 添加到组合牌中，使用中作为centerCard
                        weaveItems.add(new WeaveItem(WeaveType.Y, (byte)0x35, true, chair, true));

                        // 记录玩家仰牌信息
                        logPlayerCardInfo(chair, "仰", (byte)0);

                        // 发送操作结果事件
                        eventResponse.setMessage("仰");
                        eventResponse.setAction(MahjongConst.Y);
                        eventResponse.setCard((byte)0); // 仰操作不需要特定的card
                        eventResponse.setChair(chair);
                        eventResponse.setProvider(chair); // 仰操作的供应者是自己
                        eventResponse.setCombination(y.getCards()); // 设置中发白三张牌
                        sendTable(eventResponse);
                        addMahjongGameActionRecord(MahjongAction.Y, chair, chair, (byte)0, y.getCards(), MahjongConst.Y);

                        // 仰操作后检查当前玩家是否还能继续仰
                        Y nextY = mahjongLogic.estimateY(cardIndices[chair], chair);
                        if (nextY != null) {
                            // 当前玩家还能继续仰，发送操作通知
                            boolean hasNextAction = sendOperateNotify(chair, null, null, null, null, nextY, (byte)0, chair);
                            if (hasNextAction) {
                                // 为当前玩家启动倒计时，让其选择是否继续仰
                                startPlayCardCountdown(chair);
                                break;
                            }
                        }

                        // 当前玩家没有更多仰操作，继续检查下一个玩家
                        boolean hasNextYangAction = checkNextPlayerYangOperation();
                        if (hasNextYangAction) {
                            // 有下一个玩家可以仰牌，等待其操作
                            break;
                        }

                        // 没有任何玩家可以仰牌，庄家开始正常出牌
                        startPlayCardCountdown(banker);
                    }
                    break;
                case MahjongConst.G:  //杠
                    G g = mahjongLogic.estimateG(cardIndices[chair], card, weaveItems, false, outCardChair);
                    if (g != null) {
                        G._G destGang = getDestG(card, g);
                        if (destGang != null) {
                            MahjongKit.removeIndicesByCards(cardIndices[chair], destGang.card, destGang.card, destGang.card);//移除手上的牌
                            weaveItems.add(new WeaveItem(WeaveType.G, destGang.card, true, outCardChair, true));
                            discardCount[outCardChair] = discardCount[outCardChair] - 1;
                            discardCards[outCardChair][discardCount[outCardChair]] = 0;
                            sendGEvent(chair, outCardChair, card, eventResponse);
                            writeActionStatistic(chair, 0);  //0明杠
                            dispatchCard(chair, true);
                            
                            // 记录玩家杠牌信息
                            logPlayerCardInfo(chair, "杠", card);
                        }
                    }
                    break;
                case MahjongConst.H: //胡
                    if (grabG) { //抢杠
                        for (WeaveItem weaveItem : getWeaveItems(outCardChair)) {
                            if (weaveItem.getCenterCard() == card) {//杠设置为无效状态
                                weaveItem.setValid(false);
                            }
                        }
                    }
                    for (int targetChair : targetChairs) {
                        H h = mahjongLogic.estimateH(cardIndices[targetChair], card, weaveItems, false, outCardChair, grabG ? Source.GANG : Source.OUT, dispatchCardNumber);
                        if (h != null) {
                            sendHEvent(card, eventResponse, h, targetChair, outCardChair);
                            writeActionStatistic(targetChair, 2);  //2接炮胡
                            
                            // 记录玩家胡牌信息
                            logPlayerCardInfo(targetChair, "胡", card);
                        }
                    }
                    if (targetChairs.size() > 0) {
                        currCard = card;    //胡的那张牌
                        currChair = outCardChair;
                        gameEnd();   //游戏结束
                    }
                    break;
                default:
                    break;
            }
        } else if (currChair == chair) {  //自摸
            currCard = INVALID_CARD;
            resetCycleActionVar();
            switch (chairAction) {
                case MahjongConst.N:  //自摸过
                    eventResponse.setMessage("过");
                    eventResponse.setAction(MahjongConst.N);
                    eventResponse.setCard(card);
                    eventResponse.setChair(chair);
                    eventResponse.setProvider(chair);
                    sendChairs(eventResponse, chair);
                    addMahjongGameActionRecord(MahjongAction.N, chair, chair, card, new byte[]{}, MahjongConst.N);
                    // 自摸过后需要出牌，启动倒计时
                    startPlayCardCountdown(chair);
                    break;
                case MahjongConst.P:  //碰
                    break;
                case MahjongConst.G:  //杠
                    G g = mahjongLogic.estimateG(cardIndices[chair], card, weaveItems, true, chair);
                    if (g != null) { //可以杠
                        boolean checked = false;
                        G._G destGang = getDestG(card, g);
                        if (destGang != null) {                                                //目标杠
                            switch (destGang.w) {
                                case G.A:                                       //暗杠
                                    weaveItems.add(new WeaveItem(WeaveType.G, destGang.card, false, chair, true));
                                    MahjongKit.removeIndicesByCards(cardIndices[chair], destGang.card, destGang.card, destGang.card, destGang.card);//移除手上的牌
                                    sendGEvent(chair, chair, card, eventResponse);
                                    dispatchCard(chair, true);
                                    writeActionStatistic(chair, 1);
                                    break;
                                case G.M:                                     //明杠
                                    //这种情况不存在明杠
                                    break;
                                case G.G:                                     //拐弯杠
                                    for (WeaveItem weaveItem : weaveItems) {
                                        if (weaveItem.getWeaveType() == WeaveType.P && weaveItem.getCenterCard() == card) {
                                            weaveItem.setWeaveType(WeaveType.G);
                                            weaveItem.setProvider(chair);//发送杠牌结果
                                            weaveItem.setOpen(true);
                                            weaveItem.setValid(true);
                                            canGrabGResponse(chair, card, eventResponse);
                                            writeActionStatistic(chair, 0);
                                        }
                                    }
                                    break;
                                case G.H:                              //后拐弯杠（可以抢杠）
                                    for (WeaveItem weaveItem : weaveItems) {
                                        if (weaveItem.getWeaveType() == WeaveType.P && weaveItem.getCenterCard() == card) {
                                            weaveItem.setWeaveType(WeaveType.G);
                                            weaveItem.setProvider(chair);
                                            weaveItem.setOpen(true);
                                            weaveItem.setValid(false);  //无效状态，不算分
                                            canGrabGResponse(chair, card, eventResponse);
                                            writeActionStatistic(chair, 0);
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    break;
                case MahjongConst.H:    //胡
                    H h = mahjongLogic.estimateH(cardIndices[chair], card, weaveItems, true, chair, gOpen ? Source.GANG : Source.IN, dispatchCardNumber);
                    if (h != null) {
                        sendHEvent(card, eventResponse, h, chair, chair);
                        writeActionStatistic(chair, 3);  //自摸
                        currCard = card;    //胡的那张牌
                        currChair = chair;
                        gameEnd();   //游戏结束
                        
                        // 记录玩家胡牌信息
                        logPlayerCardInfo(chair, "胡", card);
                    }
                    break;
                case MahjongConst.Y: //仰（自摸阶段）
                    Y y_self = mahjongLogic.estimateY(cardIndices[chair], chair);
                    if (y_self != null && getTotalDiscardCount() == 0) {
                        LOGGER.info("玩家{}仰操作验证通过，移除中发白三张牌", chair);

                        // 从手牌中移除中发白三张牌
                        MahjongKit.removeIndicesByCards(cardIndices[chair], y_self.getCards());

                        // 添加到组合牌中，使用中作为centerCard
                        weaveItems.add(new WeaveItem(WeaveType.Y, (byte)0x35, true, chair, true));

                        // 记录玩家仰牌信息
                        logPlayerCardInfo(chair, "仰", (byte)0);

                        // 发送操作结果事件
                        eventResponse.setMessage("仰");
                        eventResponse.setAction(MahjongConst.Y);
                        eventResponse.setCard((byte)0); // 仰操作不需要特定的card
                        eventResponse.setChair(chair);
                        eventResponse.setProvider(chair); // 仰操作的供应者是自己
                        eventResponse.setCombination(y_self.getCards()); // 设置中发白三张牌
                        sendTable(eventResponse);
                        addMahjongGameActionRecord(MahjongAction.Y, chair, chair, (byte)0, y_self.getCards(), MahjongConst.Y);

                        // 仰操作后检查当前玩家是否还能继续仰
                        Y nextY_self = mahjongLogic.estimateY(cardIndices[chair], chair);
                        if (nextY_self != null) {
                            // 当前玩家还能继续仰，发送操作通知
                            boolean hasNextAction = sendOperateNotify(chair, null, null, null, null, nextY_self, (byte)0, chair);
                            if (hasNextAction) {
                                // 为当前玩家启动倒计时，让其选择是否继续仰
                                startPlayCardCountdown(chair);
                                break;
                            }
                        }

                        // 当前玩家没有更多仰操作，继续检查下一个玩家
                        boolean hasNextYangAction = checkNextPlayerYangOperation();
                        if (hasNextYangAction) {
                            // 有下一个玩家可以仰牌，等待其操作
                            break;
                        }

                        // 没有任何玩家可以仰牌，当前玩家需要出牌
                        startPlayCardCountdown(chair);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 胡牌事件
     *
     * @param card          card
     * @param eventResponse eventResponse
     * @param h             胡信息
     * @param targetChair   胡牌人员
     * @param provider      供应人员
     */
    private void sendHEvent(byte card, OperateResultEventResponse eventResponse, H h, int targetChair, int provider) {
        eventResponse.setMessage("胡");
        eventResponse.setAction(MahjongConst.H);
        eventResponse.setCard(card);
        eventResponse.setChair(targetChair);
        eventResponse.setProvider(provider);
        sendTable(eventResponse);
        addMahjongGameActionRecord(MahjongAction.H, targetChair, provider, card, new byte[]{}, MahjongConst.H);
        hHeroes.put(targetChair, h);
    }

    /**
     * 获取目标杠
     *
     * @param card card
     * @param g    g
     * @return G._G
     */
    private G._G getDestG(byte card, G g) {
        G._G destGang = null;
        for (G._G _g : g.gs) {
            if (_g.card == card) {    //杠的列表里存在指定的牌
                destGang = _g;
            }
        }
        return destGang;
    }

    /**
     * 能被抢杠的杠响应
     *
     * @param chair         chair
     * @param card          card
     * @param eventResponse eventResponse
     */
    private void canGrabGResponse(int chair, byte card, OperateResultEventResponse eventResponse) {
        MahjongKit.removeIndicesByCards(cardIndices[chair], card);
        sendGEvent(chair, chair, card, eventResponse);
        grabG = grabG(chair, card);//抢杠判定
    }


    /**
     * 封装杠的响应
     *
     * @param chair         玩家
     * @param provider      供应者
     * @param card          牌
     * @param eventResponse eventResponse
     */
    private void sendGEvent(int chair, int provider, byte card, OperateResultEventResponse eventResponse) {
        eventResponse.setMessage("杠");
        eventResponse.setAction(MahjongConst.G);
        eventResponse.setCard(card);
        eventResponse.setChair(chair);
        eventResponse.setProvider(provider);
        sendTable(eventResponse);
        addMahjongGameActionRecord(MahjongAction.G, chair, provider, card, new byte[]{}, MahjongConst.G);
    }

    /**
     * 抢杠判定
     *
     * @param chair chair
     * @param card  card
     */
    private boolean grabG(int chair, byte card) {
        //检测是否抢杠
        boolean hasAction = false;
        for (int i = 0; i < chairCount; i++) {
            action[i] = 0x00;
            if (i != chair) {
                H h = mahjongLogic.estimateH(cardIndices[i], card, getWeaveItems(i), false, card, Source.GANG, dispatchCardNumber);
                hasAction |= sendOperateNotify(i, null, null, h, null, card, chair);
            }
        }
        resumeChair = chair;
        outCardChair = chair;
        if (!hasAction) {
            dispatchCard(resumeChair, true);
        } else {
            currChair = Table.INVALID_CHAIR;
            // 不取消现有的倒计时，让玩家在倒计时结束前决定是否抢杠
            // 如果没有人决定抢杠，倒计时结束后会自动继续游戏
        }
        return hasAction;
    }

    /**
     * 获取所有玩家的河牌总数
     *
     * @return 河牌总数
     */
    private int getTotalDiscardCount() {
        int total = 0;
        for (int i = 0; i < chairCount; i++) {
            total += discardCount[i];
        }
        return total;
    }

    /**
     * 开始仰操作检查，从庄家开始按顺序检查
     *
     * @return 是否有玩家可以仰牌
     */
    private boolean startYangOperationCheck() {
        System.out.println("=== 开始按顺序检查仰操作 ===");
        yangCheckingChair = banker; // 从庄家开始
        yangCheckedCount = 0; // 重置已检查数量
        return checkCurrentPlayerYangOperation();
    }

    /**
     * 检查当前玩家的仰操作
     *
     * @return 是否有仰操作
     */
    private boolean checkCurrentPlayerYangOperation() {
        if (yangCheckingChair == Table.INVALID_CHAIR) {
            return false;
        }

        System.out.println("检查玩家" + yangCheckingChair + "的仰操作，当前已检查数量: " + yangCheckedCount);
        Y y = mahjongLogic.estimateY(cardIndices[yangCheckingChair], yangCheckingChair);

        if (y != null) {
            System.out.println("玩家" + yangCheckingChair + "可以进行仰操作");
            boolean sent = sendOperateNotify(yangCheckingChair, null, null, null, null, y, (byte)0, yangCheckingChair);
            if (sent) {
                // 为当前玩家启动倒计时
                startPlayCardCountdown(yangCheckingChair);
                return true;
            }
        } else {
            System.out.println("玩家" + yangCheckingChair + "不能进行仰操作");
        }

        // 当前玩家不能仰牌，检查下一个玩家
        return checkNextPlayerYangOperation();
    }

    /**
     * 检查下一个玩家的仰操作
     *
     * @return 是否有仰操作
     */
    private boolean checkNextPlayerYangOperation() {
        // 增加已检查数量
        yangCheckedCount++;

        // 如果已经检查了所有玩家，结束检查
        if (yangCheckedCount >= chairCount) {
            System.out.println("所有玩家仰操作检查完毕，没有玩家可以仰牌");
            yangCheckingChair = Table.INVALID_CHAIR;
            yangCheckedCount = 0;
            return false;
        }

        // 移动到下一个玩家
        yangCheckingChair = (yangCheckingChair + 1) % chairCount;
        System.out.println("继续检查下一个玩家: " + yangCheckingChair + "，已检查数量: " + yangCheckedCount);
        return checkCurrentPlayerYangOperation();
    }

    /**
     * 检查除了指定玩家外的其他玩家的仰操作
     * 同时通知所有可以仰牌的其他玩家
     *
     * @param excludeChair 要排除的玩家椅子号
     * @return 是否有其他玩家可以仰牌
     */
    private boolean checkOtherPlayersYangOperation(int excludeChair) {
        System.out.println("=== 开始检查其他玩家仰操作（排除玩家" + excludeChair + "）===");
        boolean hasYangAction = false;

        // 从庄家开始，检查每个玩家（排除指定玩家），给所有可以仰牌的玩家发送通知
        for (int i = 0; i < chairCount; i++) {
            int checkChair = (banker + i) % chairCount;
            if (checkChair == excludeChair) {
                continue; // 跳过指定玩家
            }

            Y y = mahjongLogic.estimateY(cardIndices[checkChair], checkChair);

            if (y != null) {
                System.out.println("玩家" + checkChair + "可以进行仰操作");
                boolean sent = sendOperateNotify(checkChair, null, null, null, null, y, (byte)0, checkChair);
                if (sent) {
                    hasYangAction = true;
                    // 不要break，继续检查其他玩家，让所有可以仰牌的玩家都收到通知
                }
            } else {
                System.out.println("玩家" + checkChair + "不能进行仰操作");
            }
        }

        System.out.println("其他玩家仰操作检查完毕，结果: " + hasYangAction);
        return hasYangAction;
    }

    /**
     * 最大椅子数
     *
     * @return int
     */
    @Override
    public int getMaxChair() {
        return MAX_CHAIR;
    }

    /**
     * @return 是否已经准备好
     */
    @Override
    public boolean isReady() { //椅子的人数与最大
        return table.clientReadyHeroesCount() == MAX_CHAIR && table.sitDownCount() == MAX_CHAIR;
    }

    /**
     * 玩家加入之后
     *
     * @param chair chair 椅子位置
     * @param hero  hero 玩家
     */
    @Override
    public void afterEnter(int chair, Hero hero) {
        LOGGER.info("用户:{}已经进入桌子，椅子编号:{}", hero.getUserName(), chair);
    }

    /**
     * 离开加入之后
     *
     * @param chair chair 椅子位置
     * @param hero  hero 玩家
     */
    @Override
    public void afterLeave(int chair, Hero hero) {
        LOGGER.info("椅子编号:{}的用户:{}已经离开桌子，", chair, hero.getUserName());
    }

    /**
     * 坐下
     *
     * @param chair chair
     * @param hero  hero
     */
    @Override
    public void sitDown(int chair, Hero hero) {
    }

    /**
     * 起立
     *
     * @param chair chair
     * @param hero  hero
     */
    @Override
    public void standUp(int chair, Hero hero) {

    }

    /**
     * 游戏结束
     */
    @Override
    public void onGameEnd() {
        //开始处理积分，等逻辑
        LOGGER.info("本局胡牌玩家有{}人!", hHeroes.values().size());
        for (Map.Entry<Integer, H> hEntry : hHeroes.entrySet()) {
            LOGGER.info("椅子编号为:{}胡牌!", hEntry.getKey());
        }
        LOGGER.info("第{}局游戏结束，总计{}局!", currGameNumber, gameNumber);
        
        // 游戏结束时，安全地清理所有倒计时资源
        if (scheduler != null) {
            LOGGER.info("游戏结束，清理所有倒计时资源");
            if (countdownFuture != null) {
                countdownFuture.cancel(true);
                countdownFuture = null;
            }
            scheduler.shutdownNow();
            scheduler = null;
            countdownChair = Table.INVALID_CHAIR;
        }
        
        gameStatus = MahjongGameStatus.GAME_PREPARE;
        calculateHScore();
        GameEndEventResponse response = ResponseFactory.success(GameEndEventResponse.class, "游戏结束");
        response.setBanker(banker);
        response.setChairCount(chairCount);
        response.setWeaveItems(getWeaveItemResponses());
        response.setProvider(currChair);
        byte[][] cards = new byte[chairCount][];
        String[] infos = new String[chairCount];
        int[] scores = new int[chairCount];
        int[] totalScores = new int[chairCount];
        for (int i = 0; i < chairCount; i++) {
            scores[i] = readScore(i, currGameNumber);
            totalScores[i] = readScore(i);
            cards[i] = MahjongKit.switchIndicesToCards(cardIndices[i]);
            if (i == currChair && hHeroes.containsKey(currChair)) {
                infos[i] = getHInfo(i);
            }
            if (i == currChair && !hHeroes.containsKey(currChair)) {
                infos[i] = "点炮";
            }
            if (i != currChair && hHeroes.containsKey(i)) {
                infos[i] = getHInfo(i);
            }
        }
        response.setScores(scores);
        response.setTotalScores(totalScores);
        response.setCards(cards);
        response.setCurrCard(currCard);
        response.setInfos(infos);
        response.setChairs(Ints.toArray(hHeroes.keySet()));
        sendTable(response);
        banker = newBanker();
        //记录分数
        mahjongGameRecord.setScores(scores);
        mahjongRecord.setTotalScores(totalScores);
        printLog();
    }

    /**
     * 判断结束
     *
     * @return boolean
     */
    @Override
    protected boolean checkEnd() {
        //结束调用end方式，不是直接调用onEnd,直接调用onEnd将无法释放table资源
        return currGameNumber >= gameNumber;
    }


    /**
     * 设置庄家
     */
    private int newBanker() {
        int banker = Table.INVALID_CHAIR;
        int min = chairCount;
        for (Integer chair : hHeroes.keySet()) {
            if (chair < banker) {
                chair = chair + chairCount;
            }
            if (Math.min(chair - currChair, min) == chair - currChair) {
                banker = chair;
            } else {
                min = chair - currChair;
            }
        }
        return banker;
    }

    /**
     * 胡牌的描述
     *
     * @param chair 椅子
     * @return string
     */
    private String getHInfo(int chair) {
        return hHeroes.get(chair).getInfo();
    }

    /**
     * 计算胡牌的分数
     */
    private void calculateHScore() {
        for (int i = 0; i < chairCount; i++) {
            if (hHeroes.containsKey(i)) {
                int maxMultiple = hHeroes.get(i).getMaxMultiple();  // 获取基础倍数
                boolean isDaHu = maxMultiple >= 4;  // 倍数>=4是大胡
                
                // 计算附加倍数
                int additionalMultiples = 1;
                
                // 当前为硬胡（无赖子），额外×2
                additionalMultiples *= 2;  // 硬胡
                
                // 统计杠的数量
                int gangCount = 0;
                for (WeaveItem weaveItem : getWeaveItems(i)) {
                    if (weaveItem.getWeaveType() == WeaveType.G && weaveItem.isValid()) {
                        gangCount++;
                    }
                }
                
                // 每个杠额外×2
                for (int g = 0; g < gangCount; g++) {
                    additionalMultiples *= 2;
                }
                
                // 基础分 × 附加倍数
                int finalMultiple = maxMultiple * additionalMultiples;
                
                if (currChair == i) { // 自摸
                    int baseScore = isDaHu ? 6 : 2;  // 大胡自摸每家底分×6，平胡自摸每家底分×2
                    int totalScore = baseScore * additionalMultiples * (chairCount - 1);
                    writeScore(currGameNumber, H_SCORE, i, totalScore);
                    
                    for (int ii = 0; ii < chairCount; ii++) {
                        if (ii != i) {
                            writeScore(currGameNumber, H_SCORE, ii, -(baseScore * additionalMultiples));
                        }
                    }
                } else {    // 点炮
                    int baseScoreForWinner = isDaHu ? 6 : 2;  // 大胡点炮点炮者底分×6，平胡点炮点炮者底分×2
                    int baseScoreForOthers = isDaHu ? 4 : 1;  // 大胡点炮其他家底分×4，平胡点炮其他家底分×1
                    
                    // 计算点炮者自己的杠数量
                    int providerGangCount = 0;
                    for (WeaveItem weaveItem : getWeaveItems(currChair)) {
                        if (weaveItem.getWeaveType() == WeaveType.G && weaveItem.isValid()) {
                            providerGangCount++;
                        }
                    }
                    
                    // 计算点炮者因杠而增加的倍数
                    int providerAdditionalMultiple = 1;
                    for (int g = 0; g < providerGangCount; g++) {
                        providerAdditionalMultiple *= 2;
                    }
                    
                    // 点炮者需要支付的分数 = 基础分 × 硬胡倍数 × 点炮者杠牌倍数
                    int providerPayScore = baseScoreForWinner * additionalMultiples * providerAdditionalMultiple;
                    writeScore(currGameNumber, H_SCORE, currChair, -providerPayScore);
                    
                    // 胡牌者得分
                    int totalScore = providerPayScore;
                    
                    // 其他人付分
                    for (int ii = 0; ii < chairCount; ii++) {
                        if (ii != i && ii != currChair) {
                            // 计算该玩家自己的杠数量
                            int playerGangCount = 0;
                            for (WeaveItem weaveItem : getWeaveItems(ii)) {
                                if (weaveItem.getWeaveType() == WeaveType.G && weaveItem.isValid()) {
                                    playerGangCount++;
                                }
                            }
                            
                            // 计算该玩家因杠而增加的倍数
                            int playerAdditionalMultiple = 1;
                            for (int g = 0; g < playerGangCount; g++) {
                                playerAdditionalMultiple *= 2;
                            }
                            
                            // 该玩家需要支付的分数 = 基础分 × 硬胡倍数 × 该玩家杠牌倍数
                            int payScore = baseScoreForOthers * additionalMultiples * playerAdditionalMultiple;
                            writeScore(currGameNumber, H_SCORE, ii, -payScore);
                            totalScore += payScore;
                        }
                    }
                    
                    writeScore(currGameNumber, H_SCORE, i, totalScore);
                }
            }
        }
    }

    /**
     * 桌子结束
     *
     * @param endReason endReason
     * @return detail
     */
    @Override
    public EndMessage onEnd(Table.EndReason endReason) {
        LOGGER.info("桌子游戏结束，释放将被全部资源，实际游戏{}局，总计{}局!", currGameNumber, gameNumber);
        switch (endReason) {
            case VOTE_DISMISS:        //投票解散
            case FORCE_DISMISS:       //强制解散
            case TIMEOUT:             //超时解散
                if (MahjongGameStatus.GAME_PLAYING == gameStatus) {
                    onGameEnd();          //强制调用onGameEnd
                }
                break;
            case NORMAL:
                break;
            default:
                break;
        }
        EndEventResponse endEventResponse = ResponseFactory.success(EndEventResponse.class, "结束");
        endEventResponse.setStartedTime(startedTime);
        endEventResponse.setEndedTime(new DateTime().toString("yyyy-MM-dd HH:mm:ss"));
        endEventResponse.setScore(readScores());
        endEventResponse.setTableNo(tableNo);
        endEventResponse.setActionStatistics(actionStatistics);
        sendTable(endEventResponse);
        printLog(); //输出日志
        return new EndMessage(Json.toJson(mahjongRecord), endReason);
    }

    /**
     * 输出日志，可用于宕机后恢复棋局
     */
    private void printLog() {
//        printKeyLog(tableNo, Json.toJson(mahjongRecord));
    }

    /**
     * 读取玩家分数
     *
     * @return scores
     */
    private int[] readScores() {
        int[] scores = new int[chairCount];
        for (int i = 0; i < chairCount; i++) {
            int score = readScore(i);
            scores[i] = score;
        }
        return scores;
    }

    /**
     * 开始出牌倒计时
     * 
     * @param chair 当前出牌玩家椅子号
     */
    private void startPlayCardCountdown(final int chair) {
        LOGGER.info("开始为玩家{}启动倒计时，当前currChair={}，countdownChair={}", chair, currChair, countdownChair);

        // 确保chair是当前可以操作的玩家
        if (chair < 0 || chair >= chairCount) {
            LOGGER.warn("尝试为无效椅子号启动倒计时: {}", chair);
            return;
        }
        
        // 如果已经有倒计时在运行，结束它
        if (scheduler != null) {
            if (countdownFuture != null) {
                countdownFuture.cancel(true);
                countdownFuture = null;
            }
            scheduler.shutdownNow();
            scheduler = null;
        }
        
        // 更新当前倒计时玩家
        countdownChair = chair;
        
        // 重置倒计时
        countdown = PLAY_CARD_TIMEOUT / 1000;
        
        LOGGER.info("开始玩家{}的倒计时，总时间{}秒", chair, countdown);
        
        // 创建新的定时执行器
        scheduler = Executors.newSingleThreadScheduledExecutor();
        
        // 创建并执行定时任务
        countdownFuture = scheduler.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                try {
                    // 打印剩余时间
                    LOGGER.info("玩家{}倒计时剩余: {}秒", chair, countdown);
                    
                    if (countdown <= 0) {
                        // 倒计时结束，取消当前定时任务
                        LOGGER.info("玩家{}倒计时结束，准备自动出牌", chair);
                        
                        // 在倒计时线程中取消定时任务，避免并发问题
                        if (countdownFuture != null) {
                            countdownFuture.cancel(false);
                            countdownFuture = null;
                        }
                        
                        // 关闭调度器但不要重置countdownChair，因为我们仍然需要它来标识当前倒计时的玩家
                        if (scheduler != null) {
                            scheduler.shutdown();
                            scheduler = null;
                        }
                        
                        // 检查是否是仰操作倒计时结束
                        if (yangCheckingChair == chair) {
                            LOGGER.info("玩家{}仰操作倒计时结束，视为放弃仰操作，检查下一个玩家", chair);
                            // 继续检查下一个玩家的仰操作
                            boolean hasNextYangAction = checkNextPlayerYangOperation();
                            if (!hasNextYangAction) {
                                // 没有更多玩家可以仰牌，庄家开始正常出牌
                                LOGGER.info("所有玩家仰操作检查完毕，庄家开始正常出牌");
                                startPlayCardCountdown(banker);
                            }
                            return;
                        }

                        // 如果当前椅子是无效的（表示有其他玩家可以操作但未操作），
                        // 则视为其他玩家放弃操作，执行下一步
                        if (currChair == Table.INVALID_CHAIR) {
                            LOGGER.info("其他玩家未在规定时间内操作，视为放弃");
                            currChair = chair; // 设置当前椅子为倒计时的玩家

                            // 如果有玩家可以操作但未操作，则轮到下一个玩家
                            if (resumeChair == chair) {
                                dispatchCard(chair, false);
                            }
                        } else {
                            // 调用自动出牌方法
                            autoPlayCard(chair);
                        }
                        return;
                    }
                    
                    // 发送倒计时消息给所有玩家
                    CountdownResponse response = ResponseFactory.success(CountdownResponse.class, "倒计时");
                    response.setRemainSeconds(countdown);
                    response.setChair(chair);
                    sendTable(response);
                    
                    // 减少倒计时
                    countdown--;
                } catch (Exception e) {
                    LOGGER.error("倒计时任务执行异常", e);
                }
            }
        }, 0, 1, TimeUnit.SECONDS);
    }

    /**
     * 取消出牌定时器
     * 
     * @param chair 要取消倒计时的玩家椅子号
     */
    private void cancelPlayCardTimer(int chair) {
        // 只有当参数chair与当前倒计时玩家匹配时才取消定时器
        if (scheduler != null && chair == countdownChair) {
            if (countdownFuture != null) {
                countdownFuture.cancel(true);
                countdownFuture = null;
            }
            scheduler.shutdownNow();
            scheduler = null;
            countdownChair = Table.INVALID_CHAIR; // 在确认取消倒计时时重置countdownChair
            LOGGER.info("尝试取消玩家{}的倒计时，当前countdownChair={}，scheduler是否为null={}",
                    chair, countdownChair, (scheduler == null));
        }
    }

    /**
     * 自动出牌（出最右边第一张牌）
     * 
     * @param chair 玩家椅子号
     */
    private void autoPlayCard(int chair) {
        try {
            // 确保是当前倒计时的玩家
            if (chair != countdownChair) {
                LOGGER.warn("尝试为非当前倒计时玩家{}自动出牌", chair);
                return;
            }
            
            // 检查玩家是否还有牌可以出
            byte[] cards = MahjongKit.switchIndicesToCards(cardIndices[chair]);
            if (cards.length <= 0) {
                LOGGER.warn("玩家{}没有可出的牌", chair);
                return;
            }
            
            // 获取最右边的牌（通常是最后一张获得的牌）
            byte card = cards[cards.length - 1];
            LOGGER.info("玩家{}倒计时结束，系统准备自动出牌:{}", chair, MahjongKit.getCardTitle(card));
            
            // 创建出牌事件
            OutCardEvent event = new OutCardEvent();
            event.setCard(card);
            
            // 获取玩家对象
            final Hero hero = table.getHero(chair);
            if (hero == null) {
                LOGGER.error("无法自动出牌：玩家{}不存在", chair);
                return;
            }
            
            // 注意：不在这里重置countdownChair，让outCard方法在验证成功后自行处理
            final int currentChair = chair;
            
            // 使用EventQueue.invokeLater确保在EDT线程中执行，避免并发问题
            EventQueue.invokeLater(new Runnable() {
                @Override
                public void run() {
                    try {
                        LOGGER.info("系统自动为玩家{}出牌:{}", currentChair, MahjongKit.getCardTitle(card));
                        outCard(currentChair, hero, event);
                    } catch (Exception e) {
                        LOGGER.error("执行自动出牌时发生异常", e);
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.error("自动出牌过程中发生异常", e);
        }
    }
}
